<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>论坛功能测试</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"], textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .checkbox-label {
            display: flex;
            align-items: center;
            font-weight: normal;
        }
        .checkbox-label input {
            margin-right: 8px;
        }
        button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #0056b3;
        }
        .post-item {
            border: 1px solid #ddd;
            border-left: 4px solid #007bff;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 4px;
        }
        .post-item.ad {
            border-left-color: #ffc107;
            background: #fff3cd;
        }
        .post-item.advice {
            border-left-color: #28a745;
            background: #d4edda;
        }
        .post-title {
            font-size: 18px;
            font-weight: 600;
            margin-bottom: 10px;
        }
        .post-meta {
            font-size: 12px;
            color: #666;
            margin-bottom: 10px;
        }
        .post-content {
            color: #555;
            line-height: 1.6;
        }
        #posts-container {
            margin-top: 30px;
        }
    </style>
</head>
<body>
    <h1>论坛功能测试</h1>
    
    <h2>发布帖子测试</h2>
    <form id="test-form">
        <div class="form-group">
            <label for="post-title">标题:</label>
            <input type="text" id="post-title" required>
        </div>
        <div class="form-group">
            <label for="post-content">内容:</label>
            <textarea id="post-content" rows="4" required></textarea>
        </div>
        <div class="form-group">
            <label class="checkbox-label">
                <input type="checkbox" id="is-ad"> 这是一条广告
            </label>
        </div>
        <div class="form-group">
            <label class="checkbox-label">
                <input type="checkbox" id="is-advice"> 这是一个建议/反馈
            </label>
        </div>
        <button type="submit">发布测试帖子</button>
    </form>
    
    <div id="posts-container">
        <h2>帖子列表</h2>
        <div id="posts-list">
            <!-- 示例帖子 -->
            <div class="post-item">
                <div class="post-title">普通帖子示例</div>
                <div class="post-meta">作者：测试用户 | 发布时间：2024-01-01 12:00</div>
                <div class="post-content">这是一个普通帖子的内容示例。</div>
            </div>
            
            <div class="post-item ad">
                <div class="post-title">广告帖子示例 [广告]</div>
                <div class="post-meta">作者：测试用户 | 发布时间：2024-01-01 12:00</div>
                <div class="post-content">这是一个广告帖子的内容示例，背景为黄色。</div>
            </div>
            
            <div class="post-item advice">
                <div class="post-title">建议反馈帖子示例 [建议/反馈]</div>
                <div class="post-meta">作者：测试用户 | 发布时间：2024-01-01 12:00</div>
                <div class="post-content">这是一个建议/反馈帖子的内容示例，背景为绿色。</div>
            </div>
        </div>
    </div>

    <script>
        // 复选框互斥逻辑
        document.addEventListener('DOMContentLoaded', function() {
            const isAdCheckbox = document.getElementById('is-ad');
            const isAdviceCheckbox = document.getElementById('is-advice');
            
            isAdCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    isAdviceCheckbox.checked = false;
                    console.log('广告选中，建议/反馈取消选中');
                }
            });
            
            isAdviceCheckbox.addEventListener('change', function() {
                if (this.checked) {
                    isAdCheckbox.checked = false;
                    console.log('建议/反馈选中，广告取消选中');
                }
            });
        });

        // 表单提交测试
        document.getElementById('test-form').addEventListener('submit', function(e) {
            e.preventDefault();
            
            const title = document.getElementById('post-title').value;
            const content = document.getElementById('post-content').value;
            const isAd = document.getElementById('is-ad').checked;
            const isAdvice = document.getElementById('is-advice').checked;
            
            console.log('表单数据:', {
                title,
                content,
                is_ad: isAd,
                is_advice: isAdvice
            });
            
            // 创建新的帖子元素进行预览
            const postClass = isAd ? 'ad' : (isAdvice ? 'advice' : '');
            const postLabel = isAd ? '[广告]' : (isAdvice ? '[建议/反馈]' : '');
            
            const newPost = document.createElement('div');
            newPost.className = `post-item ${postClass}`;
            newPost.innerHTML = `
                <div class="post-title">${title} ${postLabel}</div>
                <div class="post-meta">作者：测试用户 | 发布时间：${new Date().toLocaleString()}</div>
                <div class="post-content">${content}</div>
            `;
            
            // 添加到帖子列表顶部
            const postsList = document.getElementById('posts-list');
            postsList.insertBefore(newPost, postsList.firstChild);
            
            // 重置表单
            this.reset();
            
            alert('测试帖子已添加到列表中！');
        });
    </script>
</body>
</html>
