#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终测试脚本 - 验证所有修复
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_urllib3_compatibility():
    """测试urllib3兼容性"""
    print("1. 测试urllib3兼容性...")
    
    try:
        from app import create_compatible_retry_strategy
        retry_strategy = create_compatible_retry_strategy()
        print("✅ urllib3兼容性修复成功")
        return True
    except Exception as e:
        print(f"❌ urllib3兼容性测试失败: {e}")
        return False

def test_deepseek_api():
    """测试DeepSeek API调用"""
    print("\n2. 测试DeepSeek API调用...")
    
    try:
        from app import call_deepseek_api
        
        # 测试简单调用
        result = call_deepseek_api(
            "请回复'修复成功'", 
            "你是一个测试助手，请简短回复。",
            max_retries=1  # 减少重试次数以加快测试
        )
        
        if result:
            print(f"✅ API调用成功！AI回复: {result}")
            return True
        else:
            print("❌ API调用失败")
            return False
            
    except Exception as e:
        print(f"❌ API调用测试失败: {e}")
        return False

def test_network_config():
    """测试网络配置"""
    print("\n3. 测试网络配置...")
    
    try:
        from app import NETWORK_CONFIG
        print(f"✅ 网络配置加载成功: {NETWORK_CONFIG}")
        return True
    except Exception as e:
        print(f"❌ 网络配置测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("=" * 60)
    print("DeepSeek API 修复验证测试")
    print("=" * 60)
    
    tests = [
        test_urllib3_compatibility,
        test_network_config,
        test_deepseek_api
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ 测试异常: {e}")
    
    print("\n" + "=" * 60)
    print(f"测试结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！DeepSeek API问题已修复。")
        print("\n建议:")
        print("1. 现在可以正常使用应用了")
        print("2. 如果仍有问题，请检查网络连接和API密钥")
        print("3. 查看应用日志获取详细信息")
    else:
        print("⚠️ 部分测试失败，请检查错误信息")
        print("\n故障排除:")
        print("1. 确保所有依赖包已正确安装")
        print("2. 检查网络连接")
        print("3. 验证API密钥是否有效")

if __name__ == "__main__":
    main()
