#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DeepSeek API 连接测试脚本
用于诊断API调用超时问题
"""

import requests
import json
import time
import socket
import ssl
from urllib.parse import urlparse

# DeepSeek API配置
DEEPSEEK_API_KEY = "***********************************"
DEEPSEEK_API_URL = "https://api.deepseek.com/v1/chat/completions"

def test_network_connectivity():
    """测试网络连接"""
    print("=" * 50)
    print("1. 网络连接测试")
    print("=" * 50)
    
    # 解析URL
    parsed_url = urlparse(DEEPSEEK_API_URL)
    host = parsed_url.hostname
    port = parsed_url.port or (443 if parsed_url.scheme == 'https' else 80)
    
    print(f"目标主机: {host}")
    print(f"端口: {port}")
    
    # 测试DNS解析
    try:
        ip_address = socket.gethostbyname(host)
        print(f"✅ DNS解析成功: {host} -> {ip_address}")
    except socket.gaierror as e:
        print(f"❌ DNS解析失败: {e}")
        return False
    
    # 测试TCP连接
    try:
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(10)  # 10秒超时
        result = sock.connect_ex((host, port))
        sock.close()
        
        if result == 0:
            print(f"✅ TCP连接成功: {host}:{port}")
        else:
            print(f"❌ TCP连接失败: {host}:{port} (错误代码: {result})")
            return False
    except Exception as e:
        print(f"❌ TCP连接测试异常: {e}")
        return False
    
    # 测试SSL连接（如果是HTTPS）
    if parsed_url.scheme == 'https':
        try:
            context = ssl.create_default_context()
            with socket.create_connection((host, port), timeout=10) as sock:
                with context.wrap_socket(sock, server_hostname=host) as ssock:
                    print(f"✅ SSL连接成功")
                    print(f"   SSL版本: {ssock.version()}")
                    print(f"   证书主题: {ssock.getpeercert()['subject']}")
        except Exception as e:
            print(f"❌ SSL连接失败: {e}")
            return False
    
    return True

def test_http_request():
    """测试HTTP请求"""
    print("\n" + "=" * 50)
    print("2. HTTP请求测试")
    print("=" * 50)
    
    # 测试简单的GET请求
    try:
        print("测试GET请求...")
        response = requests.get("https://api.deepseek.com", timeout=30)
        print(f"✅ GET请求成功: 状态码 {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
    except requests.exceptions.Timeout:
        print("❌ GET请求超时")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ GET请求连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ GET请求异常: {e}")
        return False
    
    return True

def test_deepseek_api_simple():
    """测试简单的DeepSeek API调用"""
    print("\n" + "=" * 50)
    print("3. DeepSeek API 简单测试")
    print("=" * 50)
    
    headers = {
        'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    # 简单的测试数据
    data = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "user",
                "content": "Hello"
            }
        ],
        "max_tokens": 10
    }
    
    print("发送API请求...")
    print(f"URL: {DEEPSEEK_API_URL}")
    print(f"Headers: {headers}")
    print(f"Data: {json.dumps(data, indent=2, ensure_ascii=False)}")
    
    try:
        start_time = time.time()
        response = requests.post(
            DEEPSEEK_API_URL, 
            headers=headers, 
            json=data, 
            timeout=60  # 增加超时时间到60秒
        )
        end_time = time.time()
        
        print(f"✅ API请求完成，耗时: {end_time - start_time:.2f}秒")
        print(f"   状态码: {response.status_code}")
        print(f"   响应头: {dict(response.headers)}")
        
        if response.status_code == 200:
            result = response.json()
            print(f"   响应内容: {json.dumps(result, indent=2, ensure_ascii=False)}")
            return True
        else:
            print(f"   错误响应: {response.text}")
            return False
            
    except requests.exceptions.Timeout:
        print(f"❌ API请求超时 (>60秒)")
        return False
    except requests.exceptions.ConnectionError as e:
        print(f"❌ API请求连接错误: {e}")
        return False
    except Exception as e:
        print(f"❌ API请求异常: {e}")
        return False

def test_deepseek_api_with_retry():
    """测试带重试机制的DeepSeek API调用"""
    print("\n" + "=" * 50)
    print("4. DeepSeek API 重试测试")
    print("=" * 50)
    
    headers = {
        'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [
            {
                "role": "system",
                "content": "你是一个测试助手。"
            },
            {
                "role": "user",
                "content": "请回复'测试成功'"
            }
        ],
        "max_tokens": 50
    }
    
    max_retries = 3
    for attempt in range(1, max_retries + 1):
        print(f"\n尝试 {attempt}/{max_retries}...")
        
        try:
            start_time = time.time()
            
            # 使用session来复用连接
            session = requests.Session()
            session.headers.update(headers)
            
            response = session.post(
                DEEPSEEK_API_URL,
                json=data,
                timeout=(30, 60)  # (连接超时, 读取超时)
            )
            
            end_time = time.time()
            
            print(f"✅ 请求完成，耗时: {end_time - start_time:.2f}秒")
            print(f"   状态码: {response.status_code}")
            
            if response.status_code == 200:
                result = response.json()
                content = result['choices'][0]['message']['content']
                print(f"   AI回复: {content}")
                return True
            else:
                print(f"   错误响应: {response.text}")
                
        except requests.exceptions.Timeout as e:
            print(f"❌ 超时错误: {e}")
        except requests.exceptions.ConnectionError as e:
            print(f"❌ 连接错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        if attempt < max_retries:
            wait_time = 2 ** attempt  # 指数退避
            print(f"等待 {wait_time} 秒后重试...")
            time.sleep(wait_time)
    
    print("❌ 所有重试都失败了")
    return False

def test_api_key_validity():
    """测试API密钥有效性"""
    print("\n" + "=" * 50)
    print("5. API密钥有效性测试")
    print("=" * 50)
    
    # 测试无效的API密钥
    invalid_headers = {
        'Authorization': 'Bearer invalid-key',
        'Content-Type': 'application/json'
    }
    
    data = {
        "model": "deepseek-chat",
        "messages": [{"role": "user", "content": "test"}],
        "max_tokens": 10
    }
    
    try:
        response = requests.post(
            DEEPSEEK_API_URL,
            headers=invalid_headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 401:
            print("✅ API密钥验证机制正常工作")
        else:
            print(f"⚠️ 意外的响应状态码: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 测试API密钥验证时出错: {e}")
    
    # 测试当前API密钥
    print(f"\n当前API密钥: {DEEPSEEK_API_KEY}")
    print("测试当前API密钥...")
    
    valid_headers = {
        'Authorization': f'Bearer {DEEPSEEK_API_KEY}',
        'Content-Type': 'application/json'
    }
    
    try:
        response = requests.post(
            DEEPSEEK_API_URL,
            headers=valid_headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            print("✅ API密钥有效")
            return True
        elif response.status_code == 401:
            print("❌ API密钥无效或已过期")
            return False
        else:
            print(f"⚠️ API密钥测试返回状态码: {response.status_code}")
            print(f"   响应内容: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ API密钥测试出错: {e}")
        return False

def main():
    """主测试函数"""
    print("DeepSeek API 连接诊断工具")
    print("=" * 50)
    
    # 运行所有测试
    tests = [
        ("网络连接", test_network_connectivity),
        ("HTTP请求", test_http_request),
        ("API简单测试", test_deepseek_api_simple),
        ("API重试测试", test_deepseek_api_with_retry),
        ("API密钥测试", test_api_key_validity)
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        print(f"\n开始测试: {test_name}")
        try:
            result = test_func()
            results[test_name] = result
        except Exception as e:
            print(f"❌ 测试 {test_name} 时发生异常: {e}")
            results[test_name] = False
    
    # 输出总结
    print("\n" + "=" * 50)
    print("测试结果总结")
    print("=" * 50)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    # 给出建议
    print("\n" + "=" * 50)
    print("问题诊断建议")
    print("=" * 50)
    
    if not results.get("网络连接", False):
        print("🔧 网络连接问题:")
        print("   - 检查防火墙设置")
        print("   - 检查代理设置")
        print("   - 尝试使用VPN")
        
    if not results.get("API密钥测试", False):
        print("🔧 API密钥问题:")
        print("   - 检查API密钥是否正确")
        print("   - 检查API密钥是否已过期")
        print("   - 检查账户余额")
        
    if results.get("网络连接", False) and results.get("API密钥测试", False) and not results.get("API简单测试", False):
        print("🔧 API调用问题:")
        print("   - 可能是服务器负载过高")
        print("   - 尝试增加超时时间")
        print("   - 尝试添加重试机制")
        print("   - 检查请求频率限制")

if __name__ == "__main__":
    main()
