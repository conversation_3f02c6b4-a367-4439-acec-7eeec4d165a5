#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
测试论坛建议/反馈功能
"""

import requests
import json

# 测试服务器地址
BASE_URL = 'http://localhost:5000'

def test_create_advice_post():
    """测试创建建议/反馈帖子"""
    print("测试创建建议/反馈帖子...")
    
    # 测试数据
    post_data = {
        'title': '测试建议帖子',
        'content': '这是一个测试的建议帖子内容',
        'author_id': 1,
        'is_ad': False,
        'is_advice': True
    }
    
    try:
        response = requests.post(f'{BASE_URL}/forum/post', json=post_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 201
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_create_conflicting_post():
    """测试创建同时标记为广告和建议的帖子（应该失败）"""
    print("\n测试创建冲突帖子（广告+建议）...")
    
    # 测试数据
    post_data = {
        'title': '冲突测试帖子',
        'content': '这个帖子同时标记为广告和建议',
        'author_id': 1,
        'is_ad': True,
        'is_advice': True
    }
    
    try:
        response = requests.post(f'{BASE_URL}/forum/post', json=post_data)
        print(f"状态码: {response.status_code}")
        print(f"响应: {response.json()}")
        return response.status_code == 400
    except Exception as e:
        print(f"错误: {e}")
        return False

def test_get_posts():
    """测试获取帖子列表"""
    print("\n测试获取帖子列表...")
    
    try:
        response = requests.get(f'{BASE_URL}/forum/posts')
        print(f"状态码: {response.status_code}")
        posts = response.json()
        print(f"帖子数量: {len(posts)}")
        
        # 检查是否有建议帖子
        advice_posts = [post for post in posts if post.get('is_advice', False)]
        print(f"建议/反馈帖子数量: {len(advice_posts)}")
        
        if advice_posts:
            print("建议/反馈帖子示例:")
            for post in advice_posts[:2]:  # 显示前两个
                print(f"  - {post['title']}: is_advice={post.get('is_advice', False)}")
        
        return response.status_code == 200
    except Exception as e:
        print(f"错误: {e}")
        return False

if __name__ == '__main__':
    print("开始测试论坛建议/反馈功能...\n")
    
    # 运行测试
    tests = [
        test_create_advice_post,
        test_create_conflicting_post,
        test_get_posts
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    print(f"\n测试结果: {sum(results)}/{len(results)} 通过")
    
    if all(results):
        print("✅ 所有测试通过！")
    else:
        print("❌ 部分测试失败")
