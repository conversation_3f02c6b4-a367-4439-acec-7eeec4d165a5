# DeepSeek API 问题诊断和修复指南

## 问题描述
您遇到的错误：`HTTPSConnectionPool(host='api.deepseek.com', port=443): Read timed out.`

这是一个HTTP应用层的读取超时错误，表明连接已建立，但在等待服务器响应时超时。

## 已实施的修复措施

### 1. 增强的API调用函数
- ✅ 增加了重试机制（默认3次重试）
- ✅ 实现了指数退避策略
- ✅ 增加了更详细的错误处理
- ✅ 延长了超时时间（连接30秒，读取90秒）
- ✅ 添加了连接复用和适配器配置
- ✅ 支持代理设置

### 2. 网络配置优化
- ✅ 可配置的超时参数
- ✅ 自动代理检测
- ✅ SSL证书验证配置
- ✅ 连接池优化

### 3. 错误分类处理
- ✅ 区分不同类型的错误（超时、连接、认证等）
- ✅ 针对性的重试策略
- ✅ 详细的日志输出

## 测试步骤

### 步骤1：运行简化测试
```bash
# 运行简化的API测试
python simple_api_test.py

# 或者使用批处理文件
test_api.bat
```

### 步骤2：测试网络配置
```bash
# 测试网络连接
python network_config.py
```

### 步骤3：在应用中测试
启动应用后访问：`http://localhost:5000/test_deepseek_api`

## 可能的问题和解决方案

### 1. 网络连接问题
**症状**：连接超时或无法建立连接
**解决方案**：
- 检查防火墙设置
- 尝试使用VPN
- 配置代理服务器

**代理配置方法**：
```bash
# Windows
set HTTP_PROXY=http://proxy.company.com:8080
set HTTPS_PROXY=http://proxy.company.com:8080

# Linux/Mac
export HTTP_PROXY=http://proxy.company.com:8080
export HTTPS_PROXY=http://proxy.company.com:8080
```

### 2. API密钥问题
**症状**：401认证错误
**解决方案**：
- 检查API密钥是否正确
- 确认API密钥未过期
- 检查账户余额

### 3. 请求频率限制
**症状**：429错误
**解决方案**：
- 已实现自动重试和退避
- 减少并发请求数量
- 增加请求间隔

### 4. 服务器负载问题
**症状**：5xx错误或长时间无响应
**解决方案**：
- 已实现重试机制
- 增加超时时间
- 错峰使用

## 配置参数调整

如需调整网络参数，修改 `app.py` 中的 `NETWORK_CONFIG`：

```python
NETWORK_CONFIG = {
    'connect_timeout': 30,  # 连接超时（秒）
    'read_timeout': 90,     # 读取超时（秒）
    'max_retries': 3,       # 最大重试次数
    'backoff_factor': 1,    # 退避因子
    'verify_ssl': True      # SSL验证
}
```

## 监控和日志

### 查看详细日志
应用现在会输出详细的API调用日志，包括：
- 重试次数
- 响应时间
- 错误类型
- 状态码

### 健康检查
访问 `http://localhost:5000/health` 检查应用状态

## 应急备选方案

如果API持续不可用，系统会自动：
1. 使用缓存的结果（如果有）
2. 回退到关键词匹配算法
3. 提供基本的地点识别功能

## 性能优化建议

1. **启用缓存**：系统已实现2小时缓存，减少重复API调用
2. **异步处理**：地点分析在后台异步进行，不影响用户体验
3. **连接复用**：使用session复用HTTP连接
4. **智能重试**：只对可重试的错误进行重试

## 联系支持

如果问题持续存在，请：
1. 运行测试脚本收集诊断信息
2. 检查网络环境和代理设置
3. 确认API密钥和账户状态
4. 考虑联系DeepSeek技术支持

## 更新日志

- 2025-01-02：实现增强的错误处理和重试机制
- 2025-01-02：添加网络配置优化
- 2025-01-02：创建诊断测试工具
