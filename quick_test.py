#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试DeepSeek API和修复后的重试机制
"""

import sys
import os

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# 导入修复后的API调用函数
from app import call_deepseek_api

def test_api_with_retry():
    """测试带重试机制的API调用"""
    print("=" * 50)
    print("测试修复后的DeepSeek API调用")
    print("=" * 50)
    
    # 测试简单调用
    print("1. 测试简单API调用...")
    result = call_deepseek_api("请回复'API测试成功'", "你是一个测试助手，请简短回复。")
    
    if result:
        print(f"✅ API调用成功！")
        print(f"AI回复: {result}")
    else:
        print("❌ API调用失败")
    
    print("\n" + "=" * 50)
    
    # 测试地点识别功能
    print("2. 测试地点识别功能...")
    location_prompt = "我在图书馆丢了一张校园卡"
    location_system = "你是一个校园地点识别专家。请从用户输入的文本中识别出地点名称，并返回JSON格式的结果。"
    
    result2 = call_deepseek_api(location_prompt, location_system)
    
    if result2:
        print(f"✅ 地点识别成功！")
        print(f"识别结果: {result2}")
    else:
        print("❌ 地点识别失败")
    
    print("\n🎉 测试完成！")

if __name__ == "__main__":
    test_api_with_retry()
